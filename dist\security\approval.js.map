{"version": 3, "file": "approval.js", "sourceRoot": "", "sources": ["../../src/security/approval.ts"], "names": [], "mappings": ";;;;;;AAAA,mCAAsC;AACtC,wDAAgC;AAChC,kDAA0B;AAC1B,2CAAwC;AAcxC,MAAa,cAAe,SAAQ,qBAAY;IACtC,IAAI,CAAe;IACnB,oBAAoB,GAAgB,IAAI,GAAG,EAAE,CAAC;IAC9C,eAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;IAEjD,YAAY,OAAqB,SAAS;QACxC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEO,sBAAsB;QAC5B,mDAAmD;QACnD,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM;YAC5C,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;YAC/C,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ;YACzC,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;YAC/C,UAAU,EAAE,cAAc,EAAE,WAAW;YACvC,gBAAgB,EAAE,eAAe,EAAE,eAAe;SACnD,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAEhE,sEAAsE;QACtE,MAAM,iBAAiB,GAAG;YACxB,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO;YACzC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM;YAC9C,QAAQ,EAAE,UAAU,EAAE,WAAW;SAClC,CAAC;QAEF,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,OAAO,CAAC,IAAkB;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,QAAkB,EAClB,cAA8B;QAE9B,uCAAuC;QACvC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxC,QAAQ,EAAE,QAAQ,CAAC,IAAI,IAAI,WAAW;gBACtC,MAAM,EAAE,QAAQ,CAAC,EAAE,IAAI,WAAW;aACnC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAoB;YAC/B,QAAQ;YACR,cAAc;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;QAExC,8BAA8B;QAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBAChD,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;aACzC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,4CAA4C;QAC5C,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,CAAC;YACrD,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACpC,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,cAAc,CAAC,KAAK;aAChC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wBAAwB;QACxB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAEO,eAAe,CAAC,QAAkB;QACxC,OAAO,CAAC,CAAC,CACP,QAAQ;YACR,QAAQ,CAAC,EAAE;YACX,QAAQ,CAAC,IAAI;YACb,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE;YAC3B,QAAQ,CAAC,SAAS,KAAK,SAAS,CACjC,CAAC;IACJ,CAAC;IAEO,iBAAiB,CACvB,QAAkB,EAClB,cAA8B;QAE9B,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,WAAW;gBACd,OAAO,cAAc,CAAC,KAAK,KAAK,UAAU,CAAC;YAE7C,KAAK,WAAW;gBACd,iDAAiD;gBACjD,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;oBACzE,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,KAAK,KAAK,MAAM,CAAC;YAEjF,KAAK,SAAS,CAAC;YACf;gBACE,OAAO,KAAK,CAAC,CAAC,0BAA0B;QAC5C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAwB;QACvD,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QAE7C,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,eAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QAEnF,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAChE,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,cAAc,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;YAC3C,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACtC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,cAAc,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YACxC,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBAC9C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG;YACd,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;YACrC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;YAC/B,EAAE,IAAI,EAAE,uCAAuC,EAAE,KAAK,EAAE,kBAAkB,EAAE;SAC7E,CAAC;QAEF,IAAI,cAAc,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;YACxC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,wCAAwC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QACrF,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACnC;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,yBAAyB;gBAClC,OAAO;gBACP,OAAO,EAAE,cAAc,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;aAClE;SACF,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,KAAK,kBAAkB,CAAC;QAEzF,IAAI,MAAM,CAAC,QAAQ,KAAK,kBAAkB,EAAE,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACpC,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,QAAQ;YACR,SAAS,EAAE,cAAc,CAAC,KAAK;SAChC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,QAAkB;QACzC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC1C,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC;IAChF,CAAC;IAEO,gBAAgB,CAAC,QAAkB;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAEtD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC3C,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBAChE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,qBAAqB,CAAC,QAAkB;QAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAEtD,oBAAoB;QACpB,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gDAAgD;QAChD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACjD,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACnE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,QAAkB;QACtC,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAiB,CAAC;YACrD,MAAM,IAAI,GAAI,QAAQ,CAAC,SAAS,CAAC,IAAiB,IAAI,EAAE,CAAC;YACzD,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QAC/C,CAAC;QAED,OAAO,GAAG,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IAClE,CAAC;IAEO,gBAAgB,CAAC,QAAkB;QACzC,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAiB,CAAC;YACrD,MAAM,IAAI,GAAI,QAAQ,CAAC,SAAS,CAAC,IAAiB,IAAI,EAAE,CAAC;YACzD,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QAC/C,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAEO,eAAe,CAAC,KAAgB;QACtC,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,eAAK,CAAC,KAAK;YACjB,GAAG,EAAE,eAAK,CAAC,IAAI;YACf,MAAM,EAAE,eAAK,CAAC,MAAM;YACpB,IAAI,EAAE,eAAK,CAAC,GAAG;YACf,QAAQ,EAAE,eAAK,CAAC,KAAK,CAAC,KAAK;SAC5B,CAAC;QAEF,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,wBAAwB;IACxB,sBAAsB,CAAC,OAAe;QACpC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvC,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACnE,CAAC;IAED,yBAAyB,CAAC,OAAe;QACvC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC1C,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACvE,CAAC;IAED,iBAAiB,CAAC,OAAe;QAC/B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClC,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED,oBAAoB,CAAC,OAAe;QAClC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACrC,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACjE,CAAC;IAED,uBAAuB;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC/C,CAAC;IAED,kBAAkB;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC1C,CAAC;IAED,qBAAqB;QACnB,wDAAwD;QACxD,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC;YAC9B,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM;YAC5C,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;YAC/C,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ;YACzC,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;YAC/C,UAAU,EAAE,cAAc,EAAE,WAAW;YACvC,gBAAgB,EAAE,eAAe,EAAE,eAAe;SACnD,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,GAAG,eAAe,CAAC;QAC5C,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAED,4BAA4B;IAC5B,gBAAgB;QAKd,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI;YACjD,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;SACxC,CAAC;IACJ,CAAC;CACF;AAnTD,wCAmTC"}